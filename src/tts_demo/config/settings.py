"""
设置管理

提供统一的设置加载和管理功能
"""

import os
import json
from pathlib import Path
from typing import Optional
from functools import lru_cache

from dotenv import load_dotenv
from pydantic import BaseModel

from .models import AppConfig, LLMConfig, LangflowConfig, TTSConfig, WebConfig
from .constants import (
    DEFAULT_LANGFLOW_URL,
    DEFAULT_WEB_HOST,
    DEFAULT_WEB_PORT,
    DEFAULT_LLM_MODEL,
    DEFAULT_TTS_VOICE,
    DEFAULT_TTS_RATE,
    DEFAULT_TTS_VOLUME
)


class Settings(BaseModel):
    """应用设置"""

    # 环境变量配置
    default_llm_model: str = DEFAULT_LLM_MODEL
    default_tts_voice: str = DEFAULT_TTS_VOICE
    default_tts_rate: str = DEFAULT_TTS_RATE
    default_tts_volume: str = DEFAULT_TTS_VOLUME

    # OpenAI配置
    openai_api_key: Optional[str] = None
    openai_base_url: Optional[str] = None

    # Langflow配置
    langflow_base_url: str = DEFAULT_LANGFLOW_URL
    langflow_api_key: Optional[str] = None
    langflow_flow_id: Optional[str] = None

    # Web配置
    web_host: str = DEFAULT_WEB_HOST
    web_port: int = DEFAULT_WEB_PORT

    @classmethod
    def from_env(cls) -> "Settings":
        """从环境变量创建设置"""
        return cls(
            default_llm_model=os.getenv("DEFAULT_LLM_MODEL", DEFAULT_LLM_MODEL),
            default_tts_voice=os.getenv("DEFAULT_TTS_VOICE", DEFAULT_TTS_VOICE),
            default_tts_rate=os.getenv("DEFAULT_TTS_RATE", DEFAULT_TTS_RATE),
            default_tts_volume=os.getenv("DEFAULT_TTS_VOLUME", DEFAULT_TTS_VOLUME),
            openai_api_key=os.getenv("OPENAI_API_KEY"),
            openai_base_url=os.getenv("OPENAI_BASE_URL"),
            langflow_base_url=os.getenv("LANGFLOW_BASE_URL", DEFAULT_LANGFLOW_URL),
            langflow_api_key=os.getenv("LANGFLOW_API_KEY"),
            langflow_flow_id=os.getenv("LANGFLOW_FLOW_ID"),
            web_host=os.getenv("WEB_HOST", DEFAULT_WEB_HOST),
            web_port=int(os.getenv("WEB_PORT", str(DEFAULT_WEB_PORT)))
        )

    def to_app_config(self) -> AppConfig:
        """转换为应用配置"""
        return AppConfig(
            llm=LLMConfig(
                model=self.default_llm_model,
                api_key=self.openai_api_key,
                base_url=self.openai_base_url
            ),
            langflow=LangflowConfig(
                base_url=self.langflow_base_url,
                api_key=self.langflow_api_key,
                flow_id=self.langflow_flow_id
            ),
            tts=TTSConfig(
                voice=self.default_tts_voice,
                rate=self.default_tts_rate,
                volume=self.default_tts_volume
            ),
            web=WebConfig(
                host=self.web_host,
                port=self.web_port
            )
        )


@lru_cache()
def get_settings() -> Settings:
    """获取设置实例（单例模式）"""
    # 加载环境变量
    load_dotenv()
    return Settings.from_env()


def load_config_file(config_path: Optional[str] = None) -> Optional[AppConfig]:
    """从文件加载配置"""
    if config_path is None:
        # 查找默认配置文件
        possible_paths = [
            "config/app.json",
            "config/config.json",
            "app.json",
            "config.json"
        ]

        for path in possible_paths:
            if Path(path).exists():
                config_path = path
                break
        else:
            return None

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        return AppConfig(**config_data)
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return None


def save_config_file(config: AppConfig, config_path: str = "config/app.json"):
    """保存配置到文件"""
    # 确保目录存在
    Path(config_path).parent.mkdir(parents=True, exist_ok=True)

    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config.model_dump(), f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存配置文件失败: {e}")


def get_app_config(config_path: Optional[str] = None) -> AppConfig:
    """获取应用配置（优先从文件加载，否则使用环境变量）"""
    # 尝试从文件加载
    config = load_config_file(config_path)
    if config is not None:
        return config

    # 使用环境变量配置
    settings = get_settings()
    return settings.to_app_config()
