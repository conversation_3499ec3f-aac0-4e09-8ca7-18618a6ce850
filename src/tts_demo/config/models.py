"""配置数据模型

定义各种配置的数据结构
"""

from typing import Optional, Dict, Any, Literal, List
from enum import Enum
from pydantic import BaseModel, Field

from .constants import (
    DEFAULT_FIRST_CHUNK_BREAK_MODE,
    DEFAULT_LANGFLOW_URL,
    DEFAULT_WEB_HOST,
    DEFAULT_WEB_PORT,
    DEFAULT_LLM_MODEL,
    DEFAULT_TTS_VOICE,
    DEFAULT_TTS_RATE,
    DEFAULT_TTS_VOLUME,
    DEFAULT_FIRST_CHUNK_SIZE,
    DEFAULT_MIN_SENTENCE_SIZE,
    DEFAULT_MAX_CHUNK_SIZE,
    DEFAULT_QUEUE_SIZE,
    DEFAULT_MAX_QUEUE_SIZE,
    DEFAULT_CACHE_SIZE,
    DEFAULT_CACHE_MEMORY_MB
)


class FirstChunkBreakMode(str, Enum):
    """首块断句模式"""
    WORD_SEGMENTATION = "word_segmentation"  # 分词方式断句
    PUNCTUATION = "punctuation"  # 标点符号方式断句


class LLMConfig(BaseModel):
    """LLM配置"""
    model: str = Field(default=DEFAULT_LLM_MODEL, description="模型名称")
    api_key: Optional[str] = Field(default=None, description="API密钥")
    base_url: Optional[str] = Field(default=None, description="API基础URL")


class LangflowConfig(BaseModel):
    """Langflow配置"""
    base_url: str = Field(default=DEFAULT_LANGFLOW_URL, description="Langflow服务器URL")
    api_key: Optional[str] = Field(default=None, description="Langflow API密钥")
    chat_flow_id: Optional[str] = Field(default=None, description="聊天工作流ID")
    operator_flow_ids: Optional[List[str]] = Field(default=None, description="操作工作流ID列表")
    stream: bool = Field(default=True, description="是否启用流式响应")
    session_id: Optional[str] = Field(default=None, description="会话ID")
    tweaks: Optional[Dict[str, Any]] = Field(default=None, description="组件调整参数")


class TTSConfig(BaseModel):
    """TTS配置"""
    voice: str = Field(default=DEFAULT_TTS_VOICE, description="语音名称")
    rate: str = Field(default=DEFAULT_TTS_RATE, description="语速")
    volume: str = Field(default=DEFAULT_TTS_VOLUME, description="音量")


class ChunkingConfig(BaseModel):
    """文本分块配置"""
    first_chunk_size: int = Field(default=DEFAULT_FIRST_CHUNK_SIZE, description="第一个文本块的最大词数，仅在分词方式断句时使用")
    min_sentence_size: int = Field(default=DEFAULT_MIN_SENTENCE_SIZE, description="最小句子大小")
    max_chunk_size: int = Field(default=DEFAULT_MAX_CHUNK_SIZE, description="最大处理块大小")
    custom_words: Optional[str] = Field(default=None, description="自定义词语，逗号分隔")
    first_chunk_break_mode: FirstChunkBreakMode = Field(
        default=DEFAULT_FIRST_CHUNK_BREAK_MODE,
        description="首块断句模式：分词方式或标点符号方式"
    )


class PlaybackConfig(BaseModel):
    """播放配置"""
    pass  # 保留类结构，以备将来扩展


class QueueConfig(BaseModel):
    """队列配置"""
    queue_size: int = Field(default=DEFAULT_QUEUE_SIZE, description="队列初始大小")
    max_queue_size: int = Field(default=DEFAULT_MAX_QUEUE_SIZE, description="队列最大大小")


class CacheConfig(BaseModel):
    """缓存配置"""
    enable_cache: bool = Field(default=True, description="是否启用缓存")
    cache_size: int = Field(default=DEFAULT_CACHE_SIZE, description="缓存项数量限制")
    cache_memory_mb: float = Field(default=DEFAULT_CACHE_MEMORY_MB, description="缓存内存限制（MB）")


class AdvancedConfig(BaseModel):
    """高级配置"""
    stats_update_interval: float = Field(default=5.0, description="统计信息更新间隔（秒）")
    retry_attempts: int = Field(default=3, description="重试次数")
    debug: bool = Field(default=False, description="是否启用调试模式")
    save_stats: bool = Field(default=False, description="是否保存统计信息到文件")


class WebConfig(BaseModel):
    """Web界面配置"""
    host: str = Field(default=DEFAULT_WEB_HOST, description="服务器主机")
    port: int = Field(default=DEFAULT_WEB_PORT, description="服务器端口")
    reload: bool = Field(default=True, description="是否启用热重载")
    log_level: str = Field(default="info", description="日志级别")


class AppConfig(BaseModel):
    """应用总配置"""
    llm: LLMConfig = Field(default_factory=LLMConfig)
    langflow: LangflowConfig = Field(default_factory=LangflowConfig)
    tts: TTSConfig = Field(default_factory=TTSConfig)
    chunking: ChunkingConfig = Field(default_factory=ChunkingConfig)
    playback: PlaybackConfig = Field(default_factory=PlaybackConfig)
    queue: QueueConfig = Field(default_factory=QueueConfig)
    cache: CacheConfig = Field(default_factory=CacheConfig)
    advanced: AdvancedConfig = Field(default_factory=AdvancedConfig)
    web: WebConfig = Field(default_factory=WebConfig)
    use_langflow: bool = Field(default=False, description="是否使用Langflow")
